import { faker, fakerSV } from '@faker-js/faker';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../database.types';
import { StandardRibaStages, completeProjectStage } from '../project_utils';
import { capitalizeFirstLetter } from '$lib/utils';
import { getRequestEvent } from '$app/server';
import { projectShortId } from '$lib/schemas/project';

/**
 * Creates a manual budget version for demo data and activates it
 */
async function createDemoBudgetVersion(supabase: SupabaseClient<Database>, projectId: string) {
	// Create a new manual budget version for demo data
	const { data: versionId, error } = await supabase.rpc('create_budget_version', {
		p_project_id: projectId,
		p_label: 'Demo Budget Data',
		p_kind: 'manual',
	});

	if (error) {
		throw new Error(`Failed to create demo budget version: ${error.message}`);
	}

	// Activate the new version
	const { error: activateError } = await supabase.rpc('activate_budget_version', {
		p_version_id: versionId,
		p_reason: 'Demo data initialization',
	});

	if (activateError) {
		throw new Error(`Failed to activate demo budget version: ${activateError.message}`);
	}

	return versionId;
}

/**
 * Upserts a budget item into a specific budget version
 */
async function upsertBudgetVersionItem(
	supabase: SupabaseClient<Database>,
	budgetVersionId: string,
	wbsLibraryItemId: string,
	quantity: number,
	materialRate: number,
	unitRate: number,
	unitRateManualOverride: boolean = false,
	unit?: string,
	laborRate?: number,
	productivityPerHour?: number,
	factor?: number,
	remarks?: string,
) {
	// Upsert the budget version item directly
	const { data, error } = await supabase
		.from('budget_version_item')
		.upsert(
			{
				budget_version_id: budgetVersionId,
				wbs_library_item_id: wbsLibraryItemId,
				quantity,
				unit,
				material_rate: materialRate,
				labor_rate: laborRate,
				productivity_per_hour: productivityPerHour,
				unit_rate_manual_override: unitRateManualOverride,
				unit_rate: unitRate,
				factor,
				remarks,
			},
			{
				onConflict: 'budget_version_id,wbs_library_item_id',
			},
		)
		.select('budget_version_item_id')
		.single();

	if (error) {
		throw new Error(`Failed to upsert budget version item: ${error.message}`);
	}

	return data.budget_version_item_id;
}

export async function generateRibaDemoProjectData(
	supabase: SupabaseClient<Database>,
	totalBudget: number = Math.round(1_000_000_000 * (0.9 + Math.random() * 0.2)),
) {
	// Lookup the ICMS v3 WBS library id
	const { data: library } = await supabase
		.from('wbs_library')
		.select('wbs_library_id')
		.eq('name', 'ICMS v3')
		.maybeSingle();
	if (!library) throw new Error('ICMS v3 library not found');

	// Get org_id
	const org_id = getRequestEvent().locals.orgId;
	if (!org_id) throw new Error('No organization found');

	// Create a demo client
	const clientName = `${capitalizeFirstLetter(faker.color.human())} ${capitalizeFirstLetter(faker.science.chemicalElement().name)}`;
	const { error: clientError } = await supabase.from('client').insert({
		name: clientName,
		description: faker.company.catchPhrase(),
		org_id: org_id,
	});

	if (clientError) {
		console.error('Client insert failed:', clientError);
		throw new Error(`Client insert failed: ${clientError.message}`);
	}

	const { data: client } = await supabase
		.from('client')
		.select('client_id, organization (name)')
		.eq('name', clientName)
		.maybeSingle();

	if (!client) throw new Error('No client found');

	// Create project
	const projectName = fakerSV.location.streetAddress();
	console.log('Creating project with name:', projectName);
	console.log('Client ID:', client.client_id);
	console.log('WBS Library ID:', library.wbs_library_id);

	const { error: projectError } = await supabase.from('project').insert({
		name: projectName,
		description: faker.company.catchPhrase(),
		client_id: client.client_id,
		wbs_library_id: library.wbs_library_id,
	});

	if (projectError) {
		console.error('Project insert failed:', projectError);
		throw new Error(`Project insert failed: ${projectError.message}`);
	}
	console.log('Project insert successful');

	const { data: project } = await supabase
		.from('project')
		.select('project_id')
		.eq('name', projectName)
		.maybeSingle();

	if (!project) {
		console.error('No project found after insert');
		throw new Error('No project found');
	}

	const projectId = project.project_id;
	console.log('Project created successfully with ID:', projectId);

	// Create a manual budget version for demo data and activate it
	console.log('Creating demo budget version...');
	const demoBudgetVersionId = await createDemoBudgetVersion(supabase, projectId);
	console.log('Demo budget version created and activated:', demoBudgetVersionId);

	// Create stages 0-5
	const stageIds: string[] = [];
	for (let i = 0; i <= 5; i++) {
		const stage = StandardRibaStages[i];
		const { data: s } = await supabase
			.from('project_stage')
			.insert({
				project_id: projectId,
				name: stage.name,
				stage_order: stage.stage_order,
				stage: stage.stage,
			})
			.select('project_stage_id')
			.single();
		if (!s) throw new Error('No stage found');
		stageIds.push(s.project_stage_id);
	}

	// set stage 5 as construction stage
	const { error: constructionStageError } = await supabase
		.from('project')
		.update({ construction_stage_id: stageIds[5] })
		.eq('project_id', projectId);

	if (constructionStageError) {
		console.error('Construction stage update failed:', constructionStageError);
		throw new Error(`Construction stage update failed: ${constructionStageError.message}`);
	}

	// Helper to fetch WBS items
	const getItems = async (level: number, parent?: string) => {
		const query = supabase
			.from('wbs_library_item')
			.select('wbs_library_item_id, code')
			.eq('wbs_library_id', library.wbs_library_id)
			.eq('level', level);
		if (parent) query.eq('parent_item_id', parent);
		const { data } = await query;
		return data || [];
	};

	// Stage 1 - level 2 costs
	console.log('Fetching level 2 WBS items...');
	const level2 = await getItems(2);
	console.log('Level 2 items found:', level2.length);

	const acq = level2.find((i) => i.code === '01.01');
	const constr = level2.find((i) => i.code === '01.02');
	if (!acq || !constr) throw new Error('ICMS level 2 codes missing');

	const acqTotal = totalBudget * 0.3;
	const constrTotal = totalBudget * 0.7;

	console.log('Creating acquisition budget line item...', {
		project_id: projectId,
		wbs_library_item_id: acq.wbs_library_item_id,
		quantity: 1,
		material_rate: acqTotal,
		unit_rate: acqTotal,
		unit_rate_manual_override: false,
	});

	try {
		await upsertBudgetVersionItem(
			supabase,
			demoBudgetVersionId,
			acq.wbs_library_item_id,
			1,
			acqTotal,
			acqTotal,
			false,
		);
		console.log('Acquisition budget line item created successfully');
	} catch (error) {
		console.error('Error creating acquisition budget line item:', error);
		throw error;
	}

	console.log('Creating construction budget line item...', {
		project_id: projectId,
		wbs_library_item_id: constr.wbs_library_item_id,
		quantity: 1,
		material_rate: constrTotal,
		unit_rate: constrTotal,
		unit_rate_manual_override: false,
	});

	try {
		await upsertBudgetVersionItem(
			supabase,
			demoBudgetVersionId,
			constr.wbs_library_item_id,
			1,
			constrTotal,
			constrTotal,
			false,
		);
		console.log('Construction budget line item created successfully');
	} catch (error) {
		console.error('Error creating construction budget line item:', error);
		throw error;
	}

	let snapshotId = await completeProjectStage(supabase, stageIds[0], 'Stage 1 budget');

	// Utility to spread costs to child level
	const spreadToChildren = async (snapshot: string, level: number, variance: number) => {
		console.log(`Spreading costs to level ${level} with variance ${variance}...`);

		const { data: lines } = await supabase
			.from('budget_snapshot_line_item')
			.select('wbs_library_item_id, unit_rate')
			.eq('budget_snapshot_id', snapshot);

		console.log(`Found ${lines?.length || 0} snapshot line items to spread`);

		console.log('Clearing current budget version items...');
		await supabase
			.from('budget_version_item')
			.delete()
			.eq('budget_version_id', demoBudgetVersionId);

		for (const line of lines || []) {
			console.log(
				`Processing line item: ${line.wbs_library_item_id}, unit_rate: ${line.unit_rate}`,
			);

			const children = await getItems(level, line.wbs_library_item_id);
			console.log(`Found ${children.length} children at level ${level}`);

			const stageTotal = (line.unit_rate || 0) * (1 + (Math.random() * 2 - 1) * variance);
			console.log(`Stage total calculated: ${stageTotal}`);

			if (!children.length) {
				console.log('No children found, creating line item for parent...');
				try {
					await upsertBudgetVersionItem(
						supabase,
						demoBudgetVersionId,
						line.wbs_library_item_id,
						1,
						stageTotal,
						stageTotal,
						false,
					);
					console.log('Parent line item created successfully');
				} catch (error) {
					console.error('Error creating parent line item:', error);
					throw error;
				}
			} else {
				console.log(`Distributing ${stageTotal} across ${children.length} children...`);
				const base = stageTotal / children.length;
				let remaining = stageTotal;
				let idx = 0;
				for (const child of children) {
					idx++;
					const portion = idx < children.length ? base * (0.75 + Math.random() * 0.5) : remaining;
					remaining -= portion;
					console.log(
						`Creating child line item ${idx}/${children.length}: ${child.wbs_library_item_id}, portion: ${portion}`,
					);

					try {
						await upsertBudgetVersionItem(
							supabase,
							demoBudgetVersionId,
							child.wbs_library_item_id,
							1,
							Math.abs(portion),
							Math.abs(portion),
							false,
						);
						console.log(`Child line item ${idx} created successfully`);
					} catch (error) {
						console.error(`Error creating child line item ${idx}:`, error);
						throw error;
					}
				}
			}
		}
		console.log(`Finished spreading costs to level ${level}`);
	};

	// Stage 2 -> level 3 with +/-25%
	await spreadToChildren(snapshotId, 3, 0.25);
	snapshotId = await completeProjectStage(supabase, stageIds[1], 'Stage 2 budget');

	// Stage 3 -> level 4 with +/-15%
	await spreadToChildren(snapshotId, 4, 0.15);
	snapshotId = await completeProjectStage(supabase, stageIds[2], 'Stage 3 budget');

	// Stage 4 -> adjust level 4 +/-10%
	await spreadToChildren(snapshotId, 4, 0.1);
	snapshotId = await completeProjectStage(supabase, stageIds[3], 'Stage 4 budget');

	// Stage 5 -> adjust +/-5%
	await spreadToChildren(snapshotId, 4, 0.05);
	snapshotId = await completeProjectStage(supabase, stageIds[4], 'Stage 5 budget');

	// Stage 6 -> final adjust +/-5%
	await spreadToChildren(snapshotId, 5, 0.05);

	// Vendors
	const vendors: string[] = [];
	for (let i = 0; i < 30; i++) {
		const { data: v } = await supabase
			.from('vendor')
			.insert({
				name: `${capitalizeFirstLetter(faker.helpers.arrayElement([faker.animal.cow(), faker.animal.bird(), faker.animal.rabbit()])).replace(' cattle', '')} ${faker.helpers.arrayElement(
					['Contractor', 'Supplier', 'Consultants', 'Equipment Rental', 'Professional Services'],
				)}`,
				description: faker.company.catchPhrase(),
				project_id: projectId,
			})
			.select('vendor_id')
			.single();
		if (!v) throw new Error('Vendor insert & select failed');
		vendors.push(v.vendor_id);
	}

	// Get level 4 WBS items (leaf nodes) under construction for work packages
	const level4Items = await getItems(4);
	const constructionLevel4Items = level4Items.filter((item) => item.code.startsWith('01.02'));

	if (constructionLevel4Items.length === 0) {
		throw new Error('No level 4 construction WBS items found for work packages');
	}

	// Get budget amounts for each construction level 4 item from active budget version
	const { data: budgetItems } = await supabase.rpc('get_active_budget_version_items', {
		p_project_id: projectId,
	});

	// Filter to only construction level 4 items
	const filteredBudgetItems = budgetItems?.filter((item) =>
		constructionLevel4Items.some(
			(level4Item) => level4Item.wbs_library_item_id === item.wbs_library_item_id,
		),
	);

	// Create a map of budget amounts by WBS item ID
	const budgetMap = new Map<string, number>();
	filteredBudgetItems?.forEach((item) => {
		const budgetAmount = (item.unit_rate || 0) * (item.quantity || 1);
		budgetMap.set(item.wbs_library_item_id, budgetAmount);
	});

	// Work Package Creation
	const workPackagesByWbsItem = new Map<string, string[]>();
	const allWorkPackages: string[] = [];

	// Create one work package for each construction level 4 item
	for (const item of constructionLevel4Items) {
		const { data: workPackage } = await supabase
			.from('work_package')
			.insert({
				name: `${faker.commerce.productName()} - ${item.code}`,
				description: faker.commerce.productDescription(),
				project_id: projectId,
				wbs_library_item_id: item.wbs_library_item_id,
			})
			.select('work_package_id')
			.single();
		if (!workPackage) throw new Error('Work package insert & select failed');

		if (!workPackagesByWbsItem.has(item.wbs_library_item_id)) {
			workPackagesByWbsItem.set(item.wbs_library_item_id, []);
		}
		workPackagesByWbsItem.get(item.wbs_library_item_id)!.push(workPackage.work_package_id);
		allWorkPackages.push(workPackage.work_package_id);
	}

	// Create a second work package for 25% of the construction level 4 items (randomly selected)
	const secondWorkPackageItems = faker.helpers.arrayElements(
		constructionLevel4Items,
		Math.ceil(constructionLevel4Items.length * 0.25),
	);
	for (const item of secondWorkPackageItems) {
		const { data: workPackage } = await supabase
			.from('work_package')
			.insert({
				name: `${faker.commerce.productName()} - ${item.code} (Phase 2)`,
				description: faker.commerce.productDescription(),
				project_id: projectId,
				wbs_library_item_id: item.wbs_library_item_id,
			})
			.select('work_package_id')
			.single();
		if (!workPackage) throw new Error('Work package insert & select failed');

		workPackagesByWbsItem.get(item.wbs_library_item_id)!.push(workPackage.work_package_id);
		allWorkPackages.push(workPackage.work_package_id);
	}

	// Purchase Order Creation
	const purchaseOrdersByWorkPackage = new Map<string, string[]>();
	const allPurchaseOrders: { id: string; workPackageId: string; amount: number }[] = [];

	// Create one purchase order for each work package
	for (const workPackageId of allWorkPackages) {
		const vendorId = faker.helpers.arrayElement(vendors);
		const { data: po } = await supabase
			.from('purchase_order')
			.insert({
				po_number: faker.string.alphanumeric(8).toUpperCase(),
				po_date: faker.date.recent({ days: 180 }).toISOString(),
				vendor_id: vendorId,
				project_id: projectId,
				work_package_id: workPackageId,
				description: faker.commerce.productDescription(),
				original_amount: 0, // Will be set after budget allocation
			})
			.select('purchase_order_id')
			.single();
		if (!po) throw new Error('PO insert & select failed');

		if (!purchaseOrdersByWorkPackage.has(workPackageId)) {
			purchaseOrdersByWorkPackage.set(workPackageId, []);
		}
		purchaseOrdersByWorkPackage.get(workPackageId)!.push(po.purchase_order_id);
		allPurchaseOrders.push({ id: po.purchase_order_id, workPackageId, amount: 0 });
	}

	// Create a second purchase order for 50% of existing purchase orders (randomly selected)
	const secondPOWorkPackages = faker.helpers.arrayElements(
		allWorkPackages,
		Math.ceil(allWorkPackages.length * 0.5),
	);
	for (const workPackageId of secondPOWorkPackages) {
		const vendorId = faker.helpers.arrayElement(vendors);
		const { data: po } = await supabase
			.from('purchase_order')
			.insert({
				po_number: faker.string.alphanumeric(8).toUpperCase(),
				po_date: faker.date.recent({ days: 180 }).toISOString(),
				vendor_id: vendorId,
				project_id: projectId,
				work_package_id: workPackageId,
				description: faker.commerce.productDescription(),
				original_amount: 0, // Will be set after budget allocation
			})
			.select('purchase_order_id')
			.single();
		if (!po) throw new Error('PO insert & select failed');

		purchaseOrdersByWorkPackage.get(workPackageId)!.push(po.purchase_order_id);
		allPurchaseOrders.push({ id: po.purchase_order_id, workPackageId, amount: 0 });
	}

	// Create a third purchase order for 10% of existing purchase orders (randomly selected)
	const thirdPOWorkPackages = faker.helpers.arrayElements(
		allWorkPackages,
		Math.ceil(allWorkPackages.length * 0.1),
	);
	for (const workPackageId of thirdPOWorkPackages) {
		const vendorId = faker.helpers.arrayElement(vendors);
		const { data: po } = await supabase
			.from('purchase_order')
			.insert({
				po_number: faker.string.alphanumeric(8).toUpperCase(),
				po_date: faker.date.recent({ days: 180 }).toISOString(),
				vendor_id: vendorId,
				project_id: projectId,
				work_package_id: workPackageId,
				description: faker.commerce.productDescription(),
				original_amount: 0, // Will be set after budget allocation
			})
			.select('purchase_order_id')
			.single();
		if (!po) throw new Error('PO insert & select failed');

		purchaseOrdersByWorkPackage.get(workPackageId)!.push(po.purchase_order_id);
		allPurchaseOrders.push({ id: po.purchase_order_id, workPackageId, amount: 0 });
	}

	// Budget Allocation
	// Group purchase orders by WBS item and allocate budget
	for (const [wbsItemId, workPackageIds] of workPackagesByWbsItem) {
		const baseBudget = budgetMap.get(wbsItemId) || 0;
		if (baseBudget === 0) continue;

		// Apply random variance of ±10%
		const variance = Math.random() * 0.2 - 0.1; // -0.1 to +0.1
		const adjustedBudget = baseBudget * (1 + variance);

		// Get all purchase orders for this WBS item's work packages
		const relatedPOs = allPurchaseOrders.filter((po) => workPackageIds.includes(po.workPackageId));

		if (relatedPOs.length === 0) continue;

		// Distribute budget across purchase orders
		const totalShares = relatedPOs.length;
		let remainingBudget = adjustedBudget;

		for (let i = 0; i < relatedPOs.length; i++) {
			const po = relatedPOs[i];
			let allocation: number;

			if (i === relatedPOs.length - 1) {
				// Last PO gets remaining budget to ensure exact total
				allocation = remainingBudget;
			} else {
				// Random allocation between 10% and 90% of remaining budget divided by remaining POs
				const avgShare = remainingBudget / (totalShares - i);
				const minAllocation = avgShare * 0.1;
				const maxAllocation = avgShare * 1.9;
				allocation = faker.number.float({ min: minAllocation, max: maxAllocation });
				remainingBudget -= allocation;
			}

			po.amount = Math.max(0, allocation);
		}
	}

	// Update purchase order amounts in database
	for (const po of allPurchaseOrders) {
		await supabase
			.from('purchase_order')
			.update({ original_amount: Math.round(po.amount) })
			.eq('purchase_order_id', po.id);
	}

	// Invoice Creation
	const poIds: string[] = [];
	for (const po of allPurchaseOrders) {
		poIds.push(po.id);

		if (po.amount === 0) continue;

		// Create invoices with amounts totaling between 0-100% of the purchase order's original_amount
		const targetPercentage = Math.random(); // 0 to 1 (0% to 100%)
		const targetTotal = po.amount * targetPercentage;

		if (targetTotal === 0) continue;

		// Generate random number of invoices (1-5)
		const numInvoices = faker.number.int({ min: 1, max: 5 });
		let remainingAmount = targetTotal;

		for (let i = 0; i < numInvoices; i++) {
			let invoiceAmount: number;

			if (i === numInvoices - 1) {
				// Last invoice gets remaining amount
				invoiceAmount = remainingAmount;
			} else {
				// Random amount between 10% and 90% of remaining
				const minAmount = remainingAmount * 0.1;
				const maxAmount = remainingAmount * 0.9;
				invoiceAmount = faker.number.float({ min: minAmount, max: maxAmount });
				remainingAmount -= invoiceAmount;
			}

			if (invoiceAmount > 0) {
				await supabase.from('invoice').insert({
					purchase_order_id: po.id,
					description: faker.finance.transactionDescription(),
					invoice_date: faker.date.recent({ days: 15 }).toISOString(),
					account: '1000',
					amount: Math.round(invoiceAmount),
					post_date: faker.date.recent({ days: 15 }).toISOString(),
				});
			}
		}
	}

	// Risks and approved changes
	for (let i = 0; i < 25; i++) {
		const { data: risk } = await supabase
			.from('risk_register')
			.insert({
				project_id: projectId,
				title: faker.lorem.sentence(),
				description: faker.lorem.paragraph(),
				status: 'risk',
				design_fees: faker.number.int({ min: 1000, max: 10000 }),
				construction_total: faker.number.int({ min: 10000, max: 100000 }),
				date_identified: faker.date.past().toISOString(),
			})
			.select('risk_id')
			.single();

		if (!risk) throw new Error('Risk insert & select failed');

		// Randomly mark some risks as pending
		if (Math.random() < 0.4) {
			await supabase
				.from('risk_register')
				.update({ status: 'pending' })
				.eq('risk_id', risk.risk_id);
		}

		// Randomly mark some risks as approved
		if (Math.random() < 0.2) {
			await supabase
				.from('risk_register')
				.update({ status: 'approved' })
				.eq('risk_id', risk.risk_id);
		}
	}

	return {
		project_id: projectId,
		projectName: projectName,
		link: `/org/${client.organization.name}/clients/${clientName}/projects/${projectShortId(projectId)}/overview`,
	};
}

export default generateRibaDemoProjectData;
