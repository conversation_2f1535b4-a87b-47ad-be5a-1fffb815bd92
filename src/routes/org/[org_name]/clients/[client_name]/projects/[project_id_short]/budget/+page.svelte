<script lang="ts">
	import BudgetTable from '$lib/components/BudgetTable.svelte';
	import Checkbox from '$lib/components/ui/checkbox/checkbox.svelte';
	import Label from '$lib/components/ui/label/label.svelte';
	import ControlBudgetTable from '$lib/components/ControlBudgetTable.svelte';
	import BudgetVersionManager from '$lib/components/budget-version/BudgetVersionManager.svelte';
	import { Button } from '$lib/components/ui/button';
	import PlusIcon from 'phosphor-svelte/lib/Plus';
	import { useSidebar } from '$lib/components/ui/sidebar';
	import { page } from '$app/state';

	const { data } = $props();

	const sidebar = useSidebar();

	const wbsItems = $derived(data.wbsItems);
	const isConstructionStage = $derived(data.isConstructionStage);

	// State for hiding zero values
	let hideZeros = $state(true);

	// State
	let isAddingNewItem = $state(!data.rawCurrentItems || data.rawCurrentItems.length === 0);
	let showRateBreakdown = $state(false);
</script>

<div class="max-w-max overflow-hidden p-4">
	<div class="mb-4 flex items-center justify-between">
		<div class="h-8 w-full">
			<h1 class="sr-only">Project Budget</h1>
			{#if data.canEditProject && !isConstructionStage}
				<div class="flex w-full items-center justify-between">
					<div class="flex items-start gap-3">
						<a
							class="text-lg text-blue-500 underline"
							href="/org/{encodeURIComponent(page.params.org_name!)}/clients/{encodeURIComponent(
								data.client_name,
							)}/projects/{encodeURIComponent(data.project_id_short)}/budget/import"
							>Import a budget</a
						>
						<BudgetVersionManager
							projectId={data.project.project_id}
							supabase={data.supabase}
							canModifyProject={data.canEditProject}
						/>
					</div>
					<Button
						onclick={() => {
							isAddingNewItem = !isAddingNewItem;
						}}
						class="flex items-center gap-2"
					>
						<PlusIcon class="size-4" />
						<span>Add Budget Item</span>
					</Button>
				</div>
			{:else if isConstructionStage}
				<div class="flex items-center justify-between py-2">
					<BudgetVersionManager
						projectId={data.project.project_id}
						supabase={data.supabase}
						canModifyProject={data.canEditProject}
					/>
					<div class="flex items-center space-x-2">
						<Label for="hide-zeros">Show Control Budget Rate Breakdown</Label>
						<Checkbox id="hide-zeros" bind:checked={showRateBreakdown} />
					</div>
				</div>
			{/if}
		</div>
	</div>

	<div
		class="-mr-4 -ml-4 overflow-y-scroll tabular-nums"
		style={`max-width: ${
			sidebar.open ? 'calc(100vw - var(--sidebar-width) - 1rem)' : 'calc(100vw - 1rem)'
		}; max-height: calc(100vh - 11.5rem); position: relative;`}
	>
		{#if isConstructionStage}
			<ControlBudgetTable
				allWbsItems={data.allWbsItems}
				rawCurrentItems={data.rawCurrentItems}
				riskRegisterData={data.riskRegisterData}
				{hideZeros}
				showRateCalculation={showRateBreakdown}
			></ControlBudgetTable>
		{:else}
			<BudgetTable
				form={data.form}
				allWbsItems={data.allWbsItems}
				rawCurrentItems={data.rawCurrentItems}
				{wbsItems}
				{hideZeros}
				{isAddingNewItem}
			></BudgetTable>
		{/if}
	</div>
</div>
