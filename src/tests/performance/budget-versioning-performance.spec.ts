import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '$lib/database.types';

// Performance tests for budget versioning with large datasets
describe('Budget Versioning Performance Tests', () => {
	let supabase: ReturnType<typeof createClient<Database>>;
	let adminSupabase: ReturnType<typeof createClient<Database>>;
	let testProjectId: string;
	let testUserId: string;
	let testOrgId: string;
	let testClientId: string;
	let testWbsLibraryId: string;

	beforeEach(async () => {
		// Create admin Supabase client for user management and cleanup
		adminSupabase = createClient<Database>(
			process.env.PUBLIC_SUPABASE_URL!,
			process.env.SUPABASE_SERVICE_KEY!,
		);

		// Create regular Supabase client for user operations (respects RLS)
		supabase = createClient<Database>(
			process.env.PUBLIC_SUPABASE_URL!,
			process.env.PUBLIC_SUPABASE_ANON_KEY!,
		);

		await setupTestData();
	});

	afterEach(async () => {
		await cleanupTestData();
	});

	async function setupTestData() {
		// Create test user using admin client
		const { data: userData, error: userError } = await adminSupabase.auth.admin.createUser({
			email: '<EMAIL>',
			password: 'testpassword123',
			email_confirm: true,
		});

		if (userError) throw userError;
		testUserId = userData.user.id;

		// Sign in as the test user for RPC calls using regular client
		const { error: signInError } = await supabase.auth.signInWithPassword({
			email: '<EMAIL>',
			password: 'testpassword123',
		});

		if (signInError) throw signInError;

		// Ensure profile exists for the user (required for foreign key constraint)
		// Use admin client to bypass RLS for profile creation
		const { error: profileError } = await adminSupabase.from('profile').upsert({
			user_id: testUserId,
			email: '<EMAIL>',
			full_name: 'Test Performance User',
		});

		if (profileError) throw profileError;

		// Create test organization with unique name
		// Use admin client to bypass RLS for organization creation
		const uniqueOrgName = `Test Performance Org ${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
		const { data: orgData, error: orgError } = await adminSupabase
			.from('organization')
			.insert({
				name: uniqueOrgName,
				created_by_user_id: testUserId,
			})
			.select()
			.single();

		if (orgError) throw orgError;
		testOrgId = orgData.org_id;

		// Create organization membership
		await supabase.from('membership').insert({
			user_id: testUserId,
			entity_type: 'organization',
			entity_id: testOrgId,
			role: 'admin',
		});

		// Create test client
		// Use admin client to bypass RLS for client creation
		const { data: clientData, error: clientError } = await adminSupabase
			.from('client')
			.insert({
				name: 'Test Performance Client',
				org_id: testOrgId,
				created_by_user_id: testUserId,
			})
			.select()
			.single();

		if (clientError) throw clientError;
		testClientId = clientData.client_id;

		// Create test WBS library
		// Use admin client to bypass RLS for WBS library creation
		const { data: wbsLibData, error: wbsLibError } = await adminSupabase
			.from('wbs_library')
			.insert({
				name: 'Test Performance WBS',
			})
			.select()
			.single();

		if (wbsLibError) throw wbsLibError;
		testWbsLibraryId = wbsLibData.wbs_library_id;

		// Create test project
		// Use admin client to bypass RLS for project creation
		const { data: projectData, error: projectError } = await adminSupabase
			.from('project')
			.insert({
				name: 'Test Performance Project',
				client_id: testClientId,
				wbs_library_id: testWbsLibraryId,
				created_by_user_id: testUserId,
			})
			.select()
			.single();

		if (projectError) throw projectError;
		testProjectId = projectData.project_id;

		// Create large WBS structure for performance testing
		await createLargeWbsStructure();
	}

	async function createLargeWbsStructure() {
		const wbsItems = [];

		// Create 10 categories, each with 50 detail items (500 total)
		for (let cat = 1; cat <= 10; cat++) {
			// Category item
			wbsItems.push({
				wbs_library_id: testWbsLibraryId,
				level: 1,
				in_level_code: `${cat}`,
				code: `${cat}`,
				description: `Category ${cat}`,
				item_type: 'Custom' as const,
				client_id: testClientId,
			});

			// Detail items under this category
			for (let detail = 1; detail <= 50; detail++) {
				wbsItems.push({
					wbs_library_id: testWbsLibraryId,
					level: 2,
					in_level_code: `${detail}`,
					code: `${cat}.${detail}`,
					description: `Detail Item ${cat}.${detail}`,
					item_type: 'Custom' as const,
					client_id: testClientId,
				});
			}
		}

		// Insert in batches to avoid query size limits
		const batchSize = 100;
		for (let i = 0; i < wbsItems.length; i += batchSize) {
			const batch = wbsItems.slice(i, i + batchSize);
			await supabase.from('wbs_library_item').insert(batch);
		}
	}

	async function cleanupTestData() {
		// Sign out to avoid interference between tests
		await supabase.auth.signOut();

		if (testProjectId) {
			await adminSupabase.from('project').delete().eq('project_id', testProjectId);
		}
		if (testClientId) {
			await adminSupabase.from('client').delete().eq('client_id', testClientId);
		}
		if (testOrgId) {
			await adminSupabase.from('organization').delete().eq('org_id', testOrgId);
		}
		if (testUserId) {
			await adminSupabase.auth.admin.deleteUser(testUserId);
		}
	}

	describe('Large Dataset Performance', () => {
		it('should create budget version with 1000+ items efficiently', async () => {
			const startTime = Date.now();

			// Create budget version
			const { data: versionId, error: createError } = await supabase.rpc('create_budget_version', {
				p_project_id: testProjectId,
				p_label: 'Performance Test Version',
				p_kind: 'manual',
			});

			expect(createError).toBeNull();
			expect(versionId).toBeTruthy();

			// Add 1000 budget items
			const budgetItems = [];
			const { data: wbsItems } = await supabase
				.from('wbs_library_item')
				.select('wbs_library_item_id, code')
				.eq('wbs_library_id', testWbsLibraryId)
				.eq('item_type', 'Custom')
				.limit(1000);

			for (const wbsItem of wbsItems!) {
				budgetItems.push({
					budget_version_id: versionId!,
					wbs_library_item_id: wbsItem.wbs_library_item_id,
					quantity: Math.floor(Math.random() * 100) + 1,
					unit_rate: Math.floor(Math.random() * 1000) + 100,
					material_rate: 0,
					factor: 1,
				});
			}

			// Insert in batches
			const batchSize = 100;
			for (let i = 0; i < budgetItems.length; i += batchSize) {
				const batch = budgetItems.slice(i, i + batchSize);
				await supabase.from('budget_version_item').insert(batch);
			}

			const endTime = Date.now();
			const duration = endTime - startTime;

			// Should complete within reasonable time (adjust threshold as needed)
			expect(duration).toBeLessThan(30000); // 30 seconds
			console.log(`Created version with ${budgetItems.length} items in ${duration}ms`);
		});

		it('should list budget versions efficiently with large datasets', async () => {
			// Create multiple versions with data
			const versionIds = [];
			for (let i = 0; i < 5; i++) {
				const { data: versionId } = await supabase.rpc('create_budget_version', {
					p_project_id: testProjectId,
					p_label: `Performance Version ${i + 1}`,
					p_kind: 'manual',
				});
				versionIds.push(versionId);

				// Add some items to each version
				const { data: wbsItems } = await supabase
					.from('wbs_library_item')
					.select('wbs_library_item_id')
					.eq('wbs_library_id', testWbsLibraryId)
					.eq('item_type', 'Custom')
					.limit(100);

				const budgetItems = wbsItems!.map((wbsItem: any) => ({
					budget_version_id: versionId!,
					wbs_library_item_id: wbsItem.wbs_library_item_id,
					quantity: 10,
					unit_rate: 100,
					material_rate: 0,
					factor: 1,
				}));

				await supabase.from('budget_version_item').insert(budgetItems);
			}

			const startTime = Date.now();

			// List versions with aggregated data
			const { data: versions, error: listError } = await supabase.rpc('list_budget_versions', {
				p_project_id: testProjectId,
				p_limit: 50,
			});

			const endTime = Date.now();
			const duration = endTime - startTime;

			expect(listError).toBeNull();
			expect(versions).toHaveLength(6); // 5 created + 1 initial
			expect(duration).toBeLessThan(5000); // 5 seconds

			// Verify aggregated data is included
			expect(versions![0].item_count).toBeGreaterThan(0);
			expect(versions![0].total_cost).toBeGreaterThan(0);

			console.log(`Listed ${versions!.length} versions in ${duration}ms`);
		});

		it('should perform version diff efficiently with large datasets', async () => {
			// Create two versions with different large datasets
			const { data: version1Id } = await supabase.rpc('create_budget_version', {
				p_project_id: testProjectId,
				p_label: 'Diff Test Version 1',
			});

			const { data: version2Id } = await supabase.rpc('create_budget_version', {
				p_project_id: testProjectId,
				p_label: 'Diff Test Version 2',
			});

			// Add different items to each version
			const { data: wbsItems } = await supabase
				.from('wbs_library_item')
				.select('wbs_library_item_id')
				.eq('wbs_library_id', testWbsLibraryId)
				.eq('item_type', 'Custom')
				.limit(500);

			// Version 1: First 300 items
			const version1Items = wbsItems!.slice(0, 300).map((wbsItem: any) => ({
				budget_version_id: version1Id!,
				wbs_library_item_id: wbsItem.wbs_library_item_id,
				quantity: 10,
				unit_rate: 100,
				material_rate: 0,
				factor: 1,
			}));

			// Version 2: Last 300 items (200 overlap, 100 unique each)
			const version2Items = wbsItems!.slice(200, 500).map((wbsItem: any) => ({
				budget_version_id: version2Id!,
				wbs_library_item_id: wbsItem.wbs_library_item_id,
				quantity: 15, // Different quantity for changed items
				unit_rate: 120, // Different rate for changed items
				material_rate: 0,
				factor: 1,
			}));

			await supabase.from('budget_version_item').insert(version1Items);
			await supabase.from('budget_version_item').insert(version2Items);

			const startTime = Date.now();

			// Perform diff
			const { data: diff, error: diffError } = await supabase.rpc('diff_budget_versions', {
				p_version_a: version1Id!,
				p_version_b: version2Id!,
			});

			const endTime = Date.now();
			const duration = endTime - startTime;

			expect(diffError).toBeNull();
			expect(diff).toBeTruthy();
			const diffResult = diff as any;
			expect(diffResult.summary).toBeTruthy();
			expect(duration).toBeLessThan(10000); // 10 seconds

			// Verify diff results make sense
			expect(diffResult.added.length).toBeGreaterThan(0); // Items only in version 2
			expect(diffResult.removed.length).toBeGreaterThan(0); // Items only in version 1
			expect(diffResult.changed.length).toBeGreaterThan(0); // Items in both with changes

			console.log(
				`Diff completed in ${duration}ms: ${diffResult.added.length} added, ${diffResult.removed.length} removed, ${diffResult.changed.length} changed`,
			);
		});

		it('should handle large import efficiently', async () => {
			// Create large import payload (500 items)
			const importItems = [];
			for (let i = 1; i <= 500; i++) {
				importItems.push({
					code: `PERF.${i}`,
					description: `Performance Test Item ${i}`,
					quantity: Math.floor(Math.random() * 100) + 1,
					unit: 'each',
					unit_rate: Math.floor(Math.random() * 1000) + 100,
					factor: 1,
				});
			}

			const startTime = Date.now();

			// Apply large import
			const { data: importResult, error: importError } = await supabase.rpc('apply_budget_import', {
				p_project_id: testProjectId,
				p_source_filename: 'performance-test.csv',
				p_items: importItems,
				p_source_hash: 'perf-test-hash',
				p_notes: 'Performance test import',
			});

			const endTime = Date.now();
			const duration = endTime - startTime;

			expect(importError).toBeNull();
			expect(importResult).toBeTruthy();
			expect(duration).toBeLessThan(60000); // 60 seconds

			// Verify all items were imported
			const importResultData = importResult as any;
			const { data: versionItems } = await supabase
				.from('budget_version_item')
				.select('*')
				.eq('budget_version_id', importResultData.new_version_id);

			expect(versionItems).toHaveLength(500);

			console.log(`Imported ${importItems.length} items in ${duration}ms`);
		});

		it('should activate version efficiently with large datasets', async () => {
			// Create version with large dataset
			const { data: versionId } = await supabase.rpc('create_budget_version', {
				p_project_id: testProjectId,
				p_label: 'Activation Performance Test',
			});

			// Add 1000 items
			const { data: wbsItems } = await supabase
				.from('wbs_library_item')
				.select('wbs_library_item_id')
				.eq('wbs_library_id', testWbsLibraryId)
				.eq('item_type', 'Custom')
				.limit(1000);

			const budgetItems = wbsItems!.map((wbsItem: any) => ({
				budget_version_id: versionId!,
				wbs_library_item_id: wbsItem.wbs_library_item_id,
				quantity: 10,
				unit_rate: 100,
				material_rate: 0,
				factor: 1,
			}));

			await supabase.from('budget_version_item').insert(budgetItems);

			const startTime = Date.now();

			// Activate version
			const { error: activateError } = await supabase.rpc('activate_budget_version', {
				p_version_id: versionId!,
				p_reason: 'Performance test activation',
			});

			const endTime = Date.now();
			const duration = endTime - startTime;

			expect(activateError).toBeNull();
			expect(duration).toBeLessThan(5000); // 5 seconds

			// Verify activation worked
			const { data: project } = await supabase
				.from('project')
				.select('active_budget_version_id')
				.eq('project_id', testProjectId)
				.single();

			expect(project?.active_budget_version_id).toBe(versionId);

			console.log(`Activated version with ${budgetItems.length} items in ${duration}ms`);
		});
	});
});
